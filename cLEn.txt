analytics_content_sync_succeeded
analytics_de_v2_vectorized_matcher_detector_count_per_layer_total
analytics_de_v2_vectorized_matcher_udf_process_time_count
analytics_de_v2_vectorized_matcher_udf_process_time_sum
analytics_detection_engine_consumer_nacks_toal
analytics_detection_engine_events_processed_count
analytics_detection_engine_events_processed_sum
analytics_detection_final_hit_total
analytics_detection_num_of_detectors_analyzed_by_matcher_count
analytics_detection_num_of_detectors_analyzed_by_matcher_sum
analytics_detection_num_of_events_got_by_field_total
analytics_detection_num_of_events_got_to_matcher_total
analytics_detection_num_of_hits_by_detector_id_and_field_cost_total
analytics_detection_num_of_hits_by_matcher_and_detector_id_total
analytics_detection_profile_matcher_get_profile_result_time_count
analytics_detection_profile_matcher_get_profile_result_time_sum
analytics_detection_profile_matcher_profile_result_condition_time_count
analytics_detection_profile_matcher_profile_result_condition_time_sum
analytics_single_event_detection_time_count
analytics_single_event_detection_time_sum
analytics_unsuccessful_multi_events_enrichments_total
apisec_risk_engine_spec_risks_detection_duration_seconds
asset_mgmt_assoc_engine_association_conflicts_count_total
cas_applications_job_application_functions_bucket
ciem_timer_account_health_run_elapsed_time_seconds_count
ciem_timer_account_health_run_elapsed_time_seconds_sum
cold_tables_sync_dataset_failure_total
container_cpu_usage_seconds_total
container_memory_working_set_bytes
cwp_operation_duration_seconds_count
cwp_operation_duration_seconds_sum
dashboard_api_4xx_failure_total
dashboard_api_5xx_failure_total
dspm_dt_mac_findings_publish_total
emailsec_alerts_unknown_email_issue_type
itdr_data_pipeline_deleted_assets_total_rows_process_duration_bucket
itdr_data_pipeline_deleted_assets_total_rows_process_duration_count
itdr_data_pipeline_deleted_assets_total_rows_process_duration_sum
nginx_connections_active
scylla_reactor_utilization
scylla_storage_proxy_coordinator_read_latency_bucket
scylla_storage_proxy_coordinator_write_latency_bucket
st_nginx_http_request_duration_seconds_count
st_nginx_http_request_duration_seconds_sum
st_nginx_request_status_code_total
tenant_log_count_by_application
xdr_missing_alerts_in_bq
xdr_retention_service_enforce_retention_failures_total





 analytics_content_loader_data_loader_provider_data_updater_last_update_time
 analytics_content_loader_data_loader_provider_last_update_time
 analytics_content_loader_data_updater_failed_update_provider
 analytics_content_loader_data_updater_fatal_error
 analytics_content_loader_data_updater_provider_failed_update_entity
 analytics_content_loader_data_updater_provider_last_update_time
 analytics_content_loader_data_updater_provider_total_time
 analytics_detection_corrupted_events_total
 cc_cache_update_key_failure_total
 cc_cache_update_time_seconds_count